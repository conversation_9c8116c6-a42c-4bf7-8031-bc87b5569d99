## Паспорт проекта: Landing Page "Магазин Каминов" (vFinal)

### 1. Главная цель

Создать mobile-first лендинг, который мотивирует посетителя приехать в офлайн-магазин для выбора камина/печи, позиционируя это как единственно верный шаг перед покупкой.

### 2. Ключевые бизнес-задачи

1.  **Основная:** Привести посетителя в физический магазин.
2.  **Основная:** Генерировать обращения (замер, консультация, расчет) через мессенджеры и телефон.на аккаунты в соцсетях.
3.  **Второстепенная:** Стимулировать подписку на аккаунты в соцсетях.

### 3. Технологический стек

*   **Backend:** PHP 8.3+ (без фреймворков).
*   **Стилизация:** Vanilla CSS + Pico.css v2.
*   **Интерактивность:** Alpine.js v3.
*   **Подключение библиотек:** Через CDN для ускорения разработки.
*   **Принцип:** Mobile-first, централизованное управление данными через `config.php`.

### 4. Структура файлов

```
/
├── index.php
├── config.php
├── PROJECT_PASSPORT.md
├── assets/
│   ├── css/
│   │   ├── style.css         # Наши основные кастомные стили
│   │   ├── theme-light.css   # Стили для светлой темы
│   │   └── theme-dark.css    # Стили для темной темы
│   ├── images/               # Дизайн: лого, фоны, иконки
│   └── content/              # Контент: фото работ, обложки видео
└── includes/
    ├── head.php
    ├── header.php
    ├── component-category-slider.php
    ├── cookie-banner.php
    ├── footer.php
    ├── lightbox.php
    ├── modal-contact.php
    ├── section-faq.php
    ├── section-hero.php
    ├── section-social-proof.php
    └── section-map.php
```

### 5. Данные, SEO и Микроразметка

#### 5.1. Структура данных (`config.php`)

*   `$contacts`: Массив с контактами (телефоны, email, адрес, время работы, ссылки на мессенджеры и соцсети).
*   `$seo`: Массив с данными для Meta-тегов (title, description, keywords, Open Graph).
*   `$schema_data`: Массив с данными для JSON-LD разметки.
*   `$categories`: Массив категорий товаров (название, цена от, массив путей к фото).
*   `$social_galleries`: Массив для галерей соцсетей (тип, обложка, ссылка).
*   `$faq_items`: Массив вопросов и ответов для FAQ.

#### 5.2. SEO и Микроразметка (JSON-LD)

*   **Meta-теги и Open Graph:** Генерируются в `includes/head.php` из массива `$seo`.
*   **Schema `LocalBusiness`:** Основная разметка для страницы. Генерируется в `includes/head.php` из `$contacts` и `$schema_data`.
*   **Schema `FAQPage`:** Разметка для секции FAQ. Генерируется непосредственно в `includes/section-faq.php` на основе массива `$faq_items`.

### 6. Пошаговый план реализации ("Блочный подход")

1.  **Этап 0: Каркас и настройка.**
    *   Создать структуру файлов и папок.
    *   Создать `config.php` и заполнить его тестовыми данными.
    *   Создать `index.php` со всеми `include`.
    *   Реализовать `includes/head.php` с подключением библиотек (CDN), стилей, мета-тегами и Schema.org `LocalBusiness`.
    *   Реализовать логику A/B тестирования тем через GET-параметр.

2.  **Этап 1: Секция Hero.**
    *   Верстка `section-hero.php` с данными из `config.php`.
    *   Базовая стилизация в `style.css`.

3.  **Этап 2: Модальное окно и Header.**
    *   Верстка `modal-contact.php`.
    *   Логика открытия/закрытия и смены заголовка на Alpine.js.
    *   Верстка `header.php` с кнопкой, открывающей модальное окно.

4.  **Этап 3: Секция Категорий.**
    *   Верстка `section-categories.php` с циклом по массиву категорий.
    *   Реализация слайдера изображений на Alpine.js.

5.  **Этап 4: Секция Social Proof.**
    *   Верстка `section-social-proof.php` с галереями (превью-ссылки).
    *   Реализация слайдеров на Alpine.js.

6.  **Этап 5: Секция FAQ.**
    *   Верстка `section-faq.php` с циклом по массиву вопросов/ответов.
    *   Реализация аккордеона на Alpine.js и генерация разметки `FAQPage`.

7.  **Этап 6: Секция Карта и Footer.**
    *   Верстка `section-map.php` (статичная картинка-карта + контакты).
    *   Верстка `footer.php`.

8.  **Этап 7: Cookie Banner.**
    *   Верстка `cookie-banner.php`.
    *   Реализация логики показа (PHP) и скрытия/установки cookie (JS/Alpine).

9.  **Этап 8: Финальная стилизация и темы.**
    *   Наполнение `theme-light.css` и `theme-dark.css`.
    *   Проработка деталей, отступов, адаптивности.
    *   Тестирование на разных устройствах.
