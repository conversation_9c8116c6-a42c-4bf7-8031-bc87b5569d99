# Описание внешнего вида сайта "Магазин Каминов «Очаг»"

## Общая концепция дизайна

Сайт представляет собой современный одностраничный лендинг для магазина каминов и печей. Дизайн выполнен в минималистичном стиле с акцентом на визуальный контент и удобство использования. Основная цель - показать товары "вживую" и побудить посетителей приехать в физический магазин.

### Технические основы дизайна
- **CSS-фреймворк**: Pico.css v2 (минималистичный CSS-фреймворк)
- **Подход**: Mobile-first адаптивная верстка
- **Интерактивность**: Alpine.js для динамических элементов
- **Типографика**: Системные шрифты (-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto)

## Детальное описание блоков

### 1. Header (Шапка сайта)

**Расположение**: Фиксированная позиция вверху страницы (sticky)

**Визуальные характеристики**:
- Полупрозрачный фон с эффектом размытия (backdrop-filter: blur(10px))
- Начальное состояние: rgba(255, 255, 255, 0.8)
- При скролле: становится более непрозрачной rgba(255, 255, 255, 0.95) с тенью
- Плавная анимация изменения размера при скролле (transition: all 0.3s ease-in-out)

**Структура**:
- **Левая часть**: Логотип/название компании "Магазин Каминов «Очаг»" (жирный шрифт, размер 1.2rem)
- **Правая часть**: Кнопка "Связаться с нами" (контрастный стиль)

**Функциональность**:
- Уменьшается в размере при скролле страницы (управляется Alpine.js)
- Кнопка открывает модальное окно с контактами

### 2. Hero Section (Главный экран)

**Расположение**: Первый экран сайта, занимает 80% высоты viewport

**Визуальные характеристики**:
- **Фон**: Изображение hero-background.jpg с темным градиентным оверлеем
- **Градиент**: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6))
- **Выравнивание**: Центрированный контент по вертикали и горизонтали
- **Цвета текста**: Белый цвет для контраста с темным фоном

**Структура контента**:
- **Заголовок H1**: "Сайт не передаст тепло огня" (размер: 2.5rem на мобильных, 3.5rem на десктопе)
- **Подзаголовок H2**: Описание с адресом и призывом посетить магазин (размер: 1.2rem на мобильных, 1.5rem на десктопе)
- **CTA-кнопка**: "Получить консультацию" (увеличенный размер шрифта 1.1rem)

**Адаптивность**:
- На мобильных: компактное расположение, меньшие размеры шрифтов
- На планшетах и десктопе: увеличенные размеры, больше пространства

### 3. Category Sections (Секции категорий товаров)

**Количество**: Динамически генерируется для каждой категории из config.php (4 категории)

**Визуальные характеристики**:
- **Разделители**: Тонкая линия снизу каждой секции
- **Отступы**: 4rem сверху и снизу для каждой секции
- **Фон**: Стандартный фон страницы

**Структура каждой секции**:

#### Заголовочная часть:
- **Левая сторона**: 
  - Заголовок категории (H2)
  - Описание категории (параграф)
- **Правая сторона**:
  - Ценник "от X ₽" (крупный шрифт 1.75rem, жирный)
  - Кнопка "Рассчитать под ключ"

#### Слайдер изображений:
- **Формат**: Горизонтальный слайдер с превью товаров
- **Пропорции**: 4:3 для всех изображений
- **Количество видимых слайдов**:
  - Мобильные: 1 слайд
  - Планшеты: 2 слайда  
  - Десктоп: 4 слайда
- **Интерактивность**:
  - Свайп на мобильных устройствах
  - Перетаскивание мышью на десктопе
  - Hover-эффект: увеличение изображения (scale 1.05)
  - Иконка увеличения при наведении (⚲ символ в полупрозрачном круге)

**Категории товаров**:
1. **Печи-камины** - от 50 000 ₽
2. **Камины** - от 80 000 ₽  
3. **Дымоходы** - от 15 000 ₽
4. **Печи для бани** - от 25 000 ₽

### 4. Social Proof Section (Социальные сети)

**Расположение**: После секций категорий

**Визуальные характеристики**:
- **Фон**: Приглушенный фон (var(--pico-muted-background-color))
- **Отступы**: 4rem сверху и снизу
- **Выравнивание**: Центрированные заголовки

**Структура**:
- **Общий заголовок**: "Мы в социальных сетях"
- **Подзаголовок**: "Следите за нашими новыми работами, обзорами и советами"

#### Галереи социальных сетей:

**YouTube галерея**:
- **Заголовок**: "Видео обзоры на YouTube"
- **Пропорции**: 16:9 (горизонтальные видео)
- **Количество слайдов**: 3 на десктопе, 2 на планшете, 1 на мобильном
- **Иконка воспроизведения**: Отображается на превью видео

**Instagram галерея**:
- **Заголовок**: "Фото работ в Instagram"  
- **Пропорции**: 1:1.1 (квадратные с небольшим удлинением)
- **Количество слайдов**: 4 на десктопе, 2 на планшете, 1 на мобильном
- **Иконка воспроизведения**: Только для видео-постов

**TikTok галерея**:
- **Заголовок**: "Короткие видео в TikTok"
- **Пропорции**: 9:16 (вертикальные видео)
- **Количество слайдов**: 3 на десктопе, 2 на планшете, 1 на мобильном
- **Иконка воспроизведения**: На всех превью

### 5. FAQ Section (Часто задаваемые вопросы)

**Расположение**: После секции социальных сетей

**Визуальные характеристики**:
- **Фон**: Стандартный фон страницы
- **Отступы**: Стандартные отступы секции

**Структура**:
- **Заголовок**: "Ответы на частые вопросы"
- **Подзаголовок**: "Мы собрали ответы на вопросы, которые нам задают чаще всего"

#### Аккордеон вопросов:
- **Формат**: HTML элементы `<details>` и `<summary>`
- **Поведение**: Один открытый вопрос в момент времени
- **Управление**: Alpine.js для контроля состояния
- **Стилизация**: Стандартные стили Pico.css для аккордеона
- **По умолчанию**: Первый вопрос открыт

**Примеры вопросов**:
- "Можно ли установить камин в квартире?"
- "Сколько времени занимает установка?"
- "Какие документы нужны для установки?"

### 6. Map Section (Карта и контакты)

**Расположение**: Предпоследняя секция

**Визуальные характеристики**:
- **Фон**: Изображение карты (map-background.jpg) с градиентным оверлеем
- **Градиент**: 
  - Десктоп: linear-gradient(to right, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.1) 60%)
  - Мобильные: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5))
- **Отступы**: 4rem сверху и снизу

**Структура**:
- **Информационная карточка** (максимальная ширина 450px):
  - **Фон**: Полупрозрачная карточка с фоном страницы
  - **Скругление**: Стандартные скругленные углы
  - **Отступы**: 2rem внутренние отступы

#### Содержимое карточки:
- **Заголовок**: "Заезжайте в гости"
- **Подзаголовок**: "Мы всегда рады показать вам наши камины вживую"
- **Адрес**: г. Минск, пр-кт Дзержинского 115
- **Время работы**:
  - Пн-Пт: 10 – 18 (по согласованию до 22.00)
  - Суббота: 11 – 16 (по согласованию до 18.00)  
  - Воскресенье: выходной
- **Телефон**: +375 (29) 166-30-60 (кликабельная ссылка)
- **Кнопка**: "Проложить маршрут" (ведет на Яндекс.Карты)

**Адаптивность**:
- Десктоп: карточка слева, карта справа
- Мобильные: карточка по центру, полное покрытие фона

### 7. Footer (Подвал сайта)

**Расположение**: Нижняя часть страницы

**Визуальные характеристики**:
- **Фон**: Приглушенный фон (var(--pico-muted-background-color))
- **Граница**: Тонкая линия сверху
- **Отступы**: 2rem сверху и снизу

**Структура**:
- **Левая часть** (на десктопе):
  - Копирайт с текущим годом
  - Ссылка на "Политику конфиденциальности"
- **Правая часть** (на десктопе):
  - Ссылки на социальные сети (горизонтальный список)

**Адаптивность**:
- Десктоп: горизонтальное расположение с выравниванием по краям
- Мобильные: вертикальное расположение, центрированное

## Модальные окна и всплывающие элементы

### Modal Contact (Модальное окно связи)

**Триггеры**: Кнопки "Связаться с нами", "Получить консультацию", "Рассчитать под ключ"

**Визуальные характеристики**:
- **Фон**: Стандартное модальное окно Pico.css
- **Анимация**: Плавное появление/исчезновение
- **Управление**: Alpine.js

**Структура**:
- **Заголовок**: Динамически изменяется в зависимости от триггера
- **Описание**: "Выберите удобный способ для связи:"
- **Варианты связи**:
  - WhatsApp (кнопка secondary)
  - Telegram (кнопка secondary)  
  - Viber (кнопка secondary)
  - Телефон (кнопка contrast, выделенная)

### Lightbox (Просмотр изображений)

**Триггер**: Клик по изображениям в слайдерах категорий

**Визуальные характеристики**:
- **Фон**: Темный полупрозрачный оверлей
- **Центрирование**: Изображение по центру экрана
- **Максимальный размер**: 90vh - 5rem по высоте
- **Скругление**: Стандартные скругленные углы
- **Тень**: Глубокая тень для объема

**Функциональность**:
- Закрытие по Escape
- Закрытие по клику на фон
- Отображение alt-текста под изображением
- Кнопка закрытия в правом верхнем углу

### Cookie Banner (Баннер согласия на cookies)

**Условие показа**: Отображается только если cookie согласие не было дано

**Визуальные характеристики**:
- **Позиция**: Фиксированная внизу экрана
- **Фон**: Фон карточки с тенью сверху
- **Анимация**: Выезжает снизу при появлении
- **Z-index**: 2000 (поверх всех элементов)

**Структура**:
- **Текст**: Информация об использовании cookies с ссылкой на политику
- **Кнопки**:
  - "Отказаться" (secondary стиль)
  - "Принять" (основной стиль)

**Адаптивность**:
- Десктоп: горизонтальное расположение
- Мобильные: вертикальное расположение, центрированное

## Цветовая схема и стилизация

### Основные принципы:
- **Базовая палитра**: Определяется Pico.css (светлая тема)
- **Акцентные цвета**: Контрастные кнопки для важных действий
- **Фоны**: Чередование стандартного и приглушенного фонов для разделения секций
- **Текст**: Высокий контраст для читаемости

### Кнопки:
- **Основные**: Стандартный стиль Pico.css
- **Контрастные**: Для важных действий (связь, телефон)
- **Вторичные**: Для дополнительных действий
- **Скругление**: 8px для всех кнопок
- **Отступы**: 1.25rem горизонтальные
- **Шрифт**: Жирный (600)

### Анимации и переходы:
- **Плавность**: 0.3s ease-in-out для большинства переходов
- **Слайдеры**: 0.4s cubic-bezier(0.4, 0, 0.2, 1) для естественного движения
- **Hover-эффекты**: Быстрые переходы 0.3s ease
- **Модальные окна**: Fade-in/fade-out анимации

## Адаптивность и отзывчивость

### Брейкпоинты:
- **Мобильные**: до 767px
- **Планшеты**: 768px - 991px  
- **Десктоп**: 992px и выше
- **Большие экраны**: 1200px и выше

### Подход Mobile-First:
- Базовые стили для мобильных устройств
- Прогрессивное улучшение для больших экранов
- Гибкие сетки и контейнеры
- Адаптивные изображения и медиа

### Особенности адаптации:
- **Слайдеры**: Изменение количества видимых элементов
- **Типографика**: Масштабирование размеров шрифтов
- **Расположение**: Переход от вертикальных к горизонтальным макетам
- **Навигация**: Упрощение на мобильных устройствах
