# Описание логики сайта "Магазин Каминов «Очаг»"

## Архитектурная концепция

Сайт построен по принципу **модульной архитектуры** с централизованным управлением данными. Основная философия - разделение данных, логики и представления для обеспечения легкости поддержки и масштабирования.

### Технологический стек:
- **Backend**: PHP 8.3+ (без фреймворков, чистый PHP)
- **Frontend**: Alpine.js v3 для интерактивности
- **Стилизация**: Pico.css v2 + кастомные стили
- **Подход**: Server-side rendering с клиентской интерактивностью

## Структура файлов и их назначение

### Основные файлы:

#### 1. `index.php` - Главный сборочный файл
**Назначение**: Точка входа, определяет структуру страницы и порядок подключения компонентов

**Логика**:
```php
// 1. Настройка отображения ошибок для разработки
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 2. Подключение конфигурации (должен быть первым)
require_once 'config.php';

// 3. Инициализация Alpine.js состояния для скролла
x-data="{ scrolled: false }"
@scroll.window="scrolled = window.scrollY > 50"

// 4. Последовательное подключение секций
include 'includes/head.php';
include 'includes/header.php';
include 'includes/section-hero.php';

// 5. Динамическая генерация секций категорий
foreach ($categories as $category) {
    include 'includes/component-category-slider.php';
}

// 6. Подключение остальных секций и модальных окон
```

#### 2. `config.php` - Центр управления данными
**Назначение**: Единое место хранения всех данных сайта

**Структура данных**:

**Контактная информация** (`$contacts`):
- Название компании
- Адрес (структурированный: улица, город, полный адрес)
- Телефон (отображение и ссылка)
- Email
- Время работы (массив строк)
- Мессенджеры (WhatsApp, Telegram, Viber)
- Социальные сети

**SEO и микроразметка** (`$seo`, `$schema_data`):
- Meta-теги (title, description, keywords)
- Open Graph данные
- Координаты для карт
- URL логотипа

**Контент страницы**:
- `$hero_content`: Заголовки и CTA для главного экрана
- `$categories`: Массив категорий товаров с изображениями
- `$social_galleries`: Галереи социальных сетей
- `$faq_items`: Вопросы и ответы

#### 3. `includes/functions.php` - Вспомогательные функции
**Назначение**: Обработка и преобразование данных

**Ключевые функции**:
- `parseWorkHoursForSchema()`: Преобразование времени работы в Schema.org формат
- `parseWorkHourString()`: Парсинг строк времени работы
- `parseDaysOfWeek()`: Преобразование дней недели в английские названия
- `debugWorkHoursParsing()`: Отладочная функция

## Логика компонентов

### Header (`includes/header.php`)

**Функциональность**:
- Адаптивное поведение при скролле (управляется из body Alpine.js)
- Отображение логотипа и названия компании
- Кнопка открытия модального окна связи

**Структура брендинга**:
- Логотип загружается из `$schema_data['logo_url']`
- Название компании из `$contacts['company_name']`
- Вертикальное расположение: логотип сверху, текст снизу

**Логика состояний**:
```javascript
// В body определено состояние скролла
scrolled: false
@scroll.window="scrolled = window.scrollY > 50"

// Header реагирует на это состояние
:class="{ 'is-scrolled': scrolled }"
```

**Адаптивное поведение**:
- Обычное состояние: логотип 2.5rem, текст 0.875rem
- При скролле: логотип 2rem, текст 0.75rem
- На мобильных: логотип 2rem/1.75rem, текст 0.75rem/0.625rem

### Hero Section (`includes/section-hero.php`)

**Функциональность**:
- Отображение эмоционального заголовка
- CTA-кнопка с динамическим заголовком модального окна

**Логика**:
- Данные берутся из `$hero_content` массива
- Кнопка диспатчит событие `open-modal` с кастомным заголовком

### Category Slider (`includes/component-category-slider.php`)

**Назначение**: Переиспользуемый компонент для отображения категорий товаров

**Логика работы**:
1. **Проверка данных**: Валидация существования категории и изображений
2. **Инициализация слайдера**: Alpine.js компонент `carousel`
3. **Адаптивность**: Динамическое определение количества видимых слайдов
4. **Интерактивность**: Обработка свайпов, кликов и перетаскивания

**Параметры слайдера**:
```javascript
x-data="carousel({ 
    totalSlides: <?= count($images) ?>, 
    initialSlidesToShow: { 
        desktop: 4, 
        tablet: 2, 
        mobile: 1 
    } 
})"
```

**Обработка кликов**:
- Различение между свайпом и кликом
- Открытие лайтбокса только при "чистом" клике
- Предотвращение ложных срабатываний при перетаскивании

### Social Proof Section (`includes/section-social-proof.php`)

**Функциональность**:
- Отображение галерей из разных социальных сетей
- Адаптивные слайдеры с разными пропорциями

**Логика**:
1. **Итерация по галереям**: `foreach ($social_galleries as $type => $gallery)`
2. **Адаптивные настройки**: Разное количество слайдов для разных соцсетей
3. **Типизация контента**: Определение иконок воспроизведения для видео

**Особенности**:
- Instagram: 4 слайда на десктопе, пропорции 1:1
- YouTube: 3 слайда на десктопе, пропорции 16:9
- TikTok: 3 слайда на десктопе, пропорции 9:16

### FAQ Section (`includes/section-faq.php`)

**Функциональность**:
- Аккордеон с вопросами и ответами
- Генерация Schema.org микроразметки

**Логика аккордеона**:
```javascript
x-data="{ activeIndex: 0 }" // Первый вопрос открыт по умолчанию

// Для каждого элемента:
:open="activeIndex === <?= $index ?>"
@click.prevent="activeIndex = (activeIndex === <?= $index ?> ? null : <?= $index ?>)"
```

**SEO-оптимизация**:
- Автоматическая генерация JSON-LD разметки FAQPage
- Структурированные данные для поисковых систем

### Map Section (`includes/section-map.php`)

**Функциональность**:
- Отображение контактной информации
- Ссылка на построение маршрута

**Логика**:
- Формирование URL для Яндекс.Карт с координатами
- Отображение структурированной контактной информации
- Кликабельный телефон с правильным форматом ссылки

### Modal Contact (`includes/modal-contact.php`)

**Функциональность**:
- Универсальное модальное окно для связи
- Динамическое изменение заголовка

**Логика управления**:
```javascript
x-data="{ open: false, title: 'Свяжитесь с нами' }"

// Открытие через глобальное событие
@open-modal.window="
    open = true; 
    title = $event.detail.title || 'Свяжитесь с нами';
"

// Закрытие
@keydown.escape.window="open = false"
```

**Варианты связи**:
- Мессенджеры (WhatsApp, Telegram, Viber) - вторичные кнопки
- Телефон - контрастная кнопка (приоритетный способ)

### Lightbox (`includes/lightbox.php`)

**Функциональность**:
- Просмотр увеличенных изображений
- Глобальный компонент для всех галерей

**Логика**:
```javascript
// Открытие через событие
@open-lightbox.window="
    isOpen = true;
    imageSrc = $event.detail.src;
    altText = $event.detail.alt;
"

// Фокус на изображении для доступности
$nextTick(() => $refs.lightboxImage.focus());
```

### Cookie Banner (`includes/cookie-banner.php`)

**Условная загрузка**:
```php
// Показывается только если согласие не дано
if (!isset($_COOKIE['cookie_consent'])) {
    include 'includes/cookie-banner.php';
}
```

**Логика согласия**:
- **Принять**: Устанавливает cookie на 1 год
- **Отказаться**: Просто скрывает баннер на текущую сессию

## Система управления слайдерами (Carousel)

### Централизованная логика в `head.php`

**CarouselManager** - глобальный менеджер:
- Управление активным слайдером
- Предотвращение конфликтов между слайдерами
- Глобальные обработчики событий мыши и касаний

**Alpine.js компонент `carousel`**:

**Состояние**:
- `currentSlide`: Текущий слайд
- `totalSlides`: Общее количество слайдов
- `slidesToShow`: Количество видимых слайдов
- `isDragging`: Состояние перетаскивания
- `dragOffset`: Смещение при перетаскивании

**Методы**:
- `updateSlidesToShow()`: Адаптивное определение количества слайдов
- `dragStart()`: Начало перетаскивания
- `dragMove()`: Обработка движения
- `dragEnd()`: Завершение перетаскивания
- `handleClick()`: Различение клика и свайпа

**Адаптивная логика**:
```javascript
updateSlidesToShow() {
    const width = window.innerWidth;
    if (width >= 1200) this.slidesToShow = this.breakpoints.desktop;
    else if (width >= 768) this.slidesToShow = this.breakpoints.tablet;
    else this.slidesToShow = this.breakpoints.mobile;
}
```

## SEO и микроразметка

### Schema.org разметка

**LocalBusiness** (в `head.php`):
- Основная информация о компании
- Адрес и координаты
- Время работы (автоматический парсинг)
- Контактные данные

**FAQPage** (в `section-faq.php`):
- Структурированные вопросы и ответы
- Автоматическая генерация из массива `$faq_items`

### Meta-теги и Open Graph

**Стандартные meta-теги**:
- title, description, keywords
- viewport для адаптивности
- charset и язык

**Open Graph для социальных сетей**:
- og:title, og:description, og:image
- og:type, og:url, og:site_name
- og:locale для локализации

## Система обработки ошибок и отладки

### Режим разработки:
```php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
```

### Валидация данных:
- Проверка существования массивов перед использованием
- Безопасный вывод с `htmlspecialchars()`
- Проверка наличия изображений в категориях

### Отладочные функции:
- `debugWorkHoursParsing()` для проверки парсинга времени работы
- Комментарии в коде для понимания логики

## Безопасность

### Защита от XSS:
- Все выводимые данные обрабатываются `htmlspecialchars()`
- Валидация входных данных

### Безопасные ссылки:
- `target="_blank"` с `rel="noopener noreferrer"`
- Правильное формирование URL для внешних сервисов

### Cookie политика:
- Явное согласие пользователя
- Возможность отказа
- Соответствие GDPR требованиям

## Производительность

### Оптимизация загрузки:
- CDN для библиотек (Pico.css, Alpine.js)
- Lazy loading для изображений (`loading="lazy"`)
- Минимальный JavaScript footprint

### Оптимизация изображений:
- Отдельные превью и полноразмерные изображения
- Правильные alt-атрибуты для доступности
- Адаптивные пропорции

### Кэширование:
- Статические ресурсы
- Долгосрочные cookie для согласия

## Доступность (Accessibility)

### Семантическая разметка:
- Правильные HTML5 теги (header, main, section, footer)
- Структурированные заголовки (h1, h2, h3)
- Роли ARIA где необходимо

### Клавиатурная навигация:
- Фокус на модальных окнах
- Закрытие по Escape
- Правильная последовательность табуляции

### Альтернативный текст:
- Описательные alt-атрибуты для всех изображений
- Aria-label для кнопок без текста

## Масштабируемость

### Модульная архитектура:
- Независимые компоненты
- Переиспользуемые элементы
- Централизованное управление данными

### Легкость добавления контента:
- Новые категории через `$categories` массив
- Новые FAQ через `$faq_items` массив
- Новые социальные галереи через `$social_galleries`

### Возможности расширения:
- Добавление новых секций
- Интеграция с CMS
- Добавление форм обратной связи
- Интеграция с аналитикой
