<?php
/**
 * index.php
 *
 * Главный сборочный файл страницы.
 * Определяет общую структуру и порядок подключения секций.
 */

// Включаем отображение всех ошибок на этапе разработки
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Подключаем конфигурационный файл. Должен быть первым.
require_once 'config.php';

?>
<!DOCTYPE html>
<html lang="ru">

<?php 
// Подключаем секцию <head>, содержащую мета-теги, стили и скрипты
include 'includes/head.php'; 
?>

<body
    x-data="{ 
        scrolled: false,
        init() {
            // Устанавливаем начальное состояние при загрузке
            // Это сработает, даже если страница загрузилась уже проскролленной
            this.scrolled = window.scrollY > 50;
        }
    }"
    @scroll.window="scrolled = window.scrollY > 50"
>

    <?php 
    // Подключаем шапку сайта
    include 'includes/header.php'; 
    ?>

    <main>

        <?php 
        // Подключаем секцию Hero
        include 'includes/section-hero.php';

        // НОВЫЙ ПОДХОД: Динамически генерируем секции для каждой категории
        foreach ($categories as $category) {
            include 'includes/component-category-slider.php';
        }

        // Подключаем остальные секции
        include 'includes/section-social-proof.php';
        include 'includes/section-faq.php';
        include 'includes/section-map.php';
        ?>

    </main>

    <?php 
    // Подключаем подвал сайта
    include 'includes/footer.php'; 
    ?>

    <?php
    // Подключаем модальное окно для контактов
    include 'includes/modal-contact.php';

    // Подключаем баннер о cookie, только если согласие еще не было дано
    if (!isset($_COOKIE['cookie_consent'])) {
        include 'includes/cookie-banner.php';
    }
    
    // Подключаем глобальный лайтбокс для изображений
    include 'includes/lightbox.php';
    ?>

</body>
</html>