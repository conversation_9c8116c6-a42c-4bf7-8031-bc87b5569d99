<?php
/**
 * section-faq.php
 *
 * Секция с часто задаваемыми вопросами (FAQ).
 * - Реализована в виде аккордеона на Alpine.js.
 * - Генерирует микроразметку Schema.org/FAQPage для SEO.
 */

// Подготовка данных для JSON-LD
$faq_schema = [
    '@context' => 'https://schema.org',
    '@type' => 'FAQPage',
    'mainEntity' => []
];
foreach ($faq_items as $item) {
    $faq_schema['mainEntity'][] = [
        '@type' => 'Question',
        'name' => $item['question'],
        'acceptedAnswer' => [
            '@type' => 'Answer',
            'text' => $item['answer']
        ]
    ];
}
?>
<section id="faq">
    <div class="container">
        <hgroup>
            <h2>Ответы на частые вопросы</h2>
            <p>Мы собрали ответы на вопросы, которые нам задают чаще всего.</p>
        </hgroup>

        <div class="faq-accordion" x-data="{ activeIndex: 0 }">
            <?php foreach ($faq_items as $index => $item): ?>
                <details :open="activeIndex === <?= $index ?>">
                    <summary @click.prevent="activeIndex = (activeIndex === <?= $index ?> ? null : <?= $index ?>)">
                        <?= htmlspecialchars($item['question']) ?>
                    </summary>
                    <p><?= nl2br(htmlspecialchars($item['answer'])) ?></p>
                </details>
            <?php endforeach; ?>
        </div>
    </div>

    <!-- JSON-LD Микроразметка для SEO -->
    <script type="application/ld+json">
        <?= json_encode($faq_schema, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>
    </script>
</section>
