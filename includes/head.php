<?php
/**
 * head.php
 *
 * Формирует секцию <head> страницы.
 * - Подключает стили и скрипты.
 * - Выводит мета-теги для SEO.
 * - Реализует логику выбора темы.
 * - Генерирует JSON-LD микроразметку.
 */

// Подключаем вспомогательные функции
require_once __DIR__ . '/functions.php';

// --- Логика выбора темы для A/B теста ---
$default_theme = 'light';
$allowed_themes = ['light', 'dark'];
$current_theme = $default_theme;

if (isset($_GET['theme']) && in_array($_GET['theme'], $allowed_themes)) {
    $current_theme = $_GET['theme'];
}

// --- Формирование URL для Open Graph и Canonical ---
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

?>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- Основные SEO-теги -->
    <title><?= htmlspecialchars($seo['title']) ?></title>
    <meta name="description" content="<?= htmlspecialchars($seo['description']) ?>">
    <meta name="keywords" content="<?= htmlspecialchars($seo['keywords']) ?>">
    <link rel="canonical" href="<?= $base_url ?>">

    <!-- Open Graph для соцсетей -->
    <meta property="og:title" content="<?= htmlspecialchars($seo['og_title']) ?>">
    <meta property="og:description" content="<?= htmlspecialchars($seo['og_description']) ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $base_url ?>">
    <meta property="og:image" content="<?= $base_url . htmlspecialchars($seo['og_image_url']) ?>">
    <meta property="og:site_name" content="<?= htmlspecialchars($contacts['company_name']) ?>">
    <meta property="og:locale" content="ru_RU">

    <!-- Подключение библиотек через CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@2/css/pico.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Подключение кастомных стилей -->
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/theme-<?= $current_theme ?>.css">

    <!-- JSON-LD Микроразметка (Schema.org) -->
    <script type="application/ld+json">
    <?= json_encode([
        '@context' => 'https://schema.org',
        '@type' => 'HomeAndConstructionBusiness',
        'name' => $contacts['company_name'],
        'description' => $seo['description'],
        'image' => $base_url . htmlspecialchars($schema_data['logo_url']),
        'logo' => $base_url . htmlspecialchars($schema_data['logo_url']),
        'url' => $base_url,
        'telephone' => $contacts['phone']['link'],
        'email' => $contacts['email'],
        'address' => [
            '@type' => 'PostalAddress',
            'streetAddress' => $contacts['address']['street'],
            'addressLocality' => $contacts['address']['city'],
            'addressCountry' => 'RU'
        ],
        'geo' => [
            '@type' => 'GeoCoordinates',
            'latitude' => $schema_data['geo']['latitude'],
            'longitude' => $schema_data['geo']['longitude']
        ],
        'openingHoursSpecification' => parseWorkHoursForSchema($contacts['work_hours'])
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>
    </script>

<script>
document.addEventListener('alpine:initializing', () => {
    // Централизованное управление глобальными слушателями
    const CarouselManager = {
        activeCarousel: null,
        listenersAttached: false,

        setActiveCarousel(carousel) {
            this.activeCarousel = carousel;
            if (!this.listenersAttached) {
                this.attachGlobalListeners();
            }
        },

        attachGlobalListeners() {
            window.addEventListener('mousemove', (e) => {
                if (this.activeCarousel) {
                    this.activeCarousel.dragMove(e);
                }
            });

            window.addEventListener('mouseup', (e) => {
                if (this.activeCarousel) {
                    this.activeCarousel.dragEnd(e);
                }
            });

            window.addEventListener('touchmove', (e) => {
                if (this.activeCarousel) {
                    this.activeCarousel.dragMove(e);
                }
            }, { passive: true });

            window.addEventListener('touchend', (e) => {
                if (this.activeCarousel) {
                    this.activeCarousel.dragEnd(e);
                }
            });

            this.listenersAttached = true;
        }
    };

    Alpine.data('carousel', (options) => ({
        // --- Состояние ---
        currentSlide: 0,
        totalSlides: options.totalSlides,
        slidesToShow: 1,
        
        // --- Состояние для свайпа и тапа ---
        isDragging: false,
        dragStartX: 0,
        dragCurrentX: 0,
        dragOffset: 0,
        dragThreshold: 50,
        clickAllowed: true, // Флаг, разрешающий клик/тап

        // --- Инициализация ---
        init() {
            this.updateSlidesToShow();
            window.addEventListener('resize', () => this.updateSlidesToShow());
        },

        // --- Адаптивность ---
        updateSlidesToShow() {
            if (window.innerWidth >= 1200 && options.initialSlidesToShow.desktop) {
                this.slidesToShow = options.initialSlidesToShow.desktop;
            } else if (window.innerWidth >= 768 && options.initialSlidesToShow.tablet) {
                this.slidesToShow = options.initialSlidesToShow.tablet;
            } else {
                this.slidesToShow = options.initialSlidesToShow.mobile;
            }
            if (this.currentSlide > this.totalSlides - this.slidesToShow) {
                this.currentSlide = Math.max(0, this.totalSlides - this.slidesToShow);
            }
        },

        // --- Навигация кнопками ---
        next() {
            if (this.currentSlide < this.totalSlides - this.slidesToShow) { this.currentSlide++; } 
            else { this.currentSlide = 0; }
        },
        prev() {
            if (this.currentSlide > 0) { this.currentSlide--; } 
            else { this.currentSlide = Math.max(0, this.totalSlides - this.slidesToShow); }
        },

        // --- Логика свайпа и тапа ---
        dragStart(e) {
            this.isDragging = true;
            this.clickAllowed = true; // Разрешаем клик по умолчанию
            this.dragStartX = e.pageX || e.touches[0].pageX;
            this.dragCurrentX = this.dragStartX;

            // Регистрируем этот слайдер как активный
            CarouselManager.setActiveCarousel(this);
        },

        dragMove(e) {
            if (!this.isDragging) return;
            this.dragCurrentX = e.pageX || e.touches[0].pageX;
            this.dragOffset = this.dragCurrentX - this.dragStartX;

            // Если движение превысило небольшой порог, считаем это свайпом и запрещаем клик
            if (Math.abs(this.dragOffset) > 10) {
                this.clickAllowed = false;
            }
        },

        dragEnd() {
            if (!this.isDragging) return;

            if (Math.abs(this.dragOffset) > this.dragThreshold) {
                if (this.dragOffset < 0) { this.next(); }
                else { this.prev(); }
            }

            this.isDragging = false;
            this.dragOffset = 0;

            // Сбрасываем активный слайдер
            CarouselManager.activeCarousel = null;
        },

        // --- Обработчик клика/тапа ---
        handleClick(targetUrl, eventType = 'external-link', altText = '') {
            if (this.clickAllowed) {
                if (eventType === 'lightbox') {
                    // Для лайтбокса
                    this.$dispatch('open-lightbox', { src: targetUrl, alt: altText });
                } else {
                    // Для внешних ссылок
                    window.open(targetUrl, '_blank');
                }
            }
            // Сбрасываем флаг после использования
            this.clickAllowed = true;
        },

        // --- Динамический стиль для transform ---
        get transformStyle() {
            const baseOffset = -this.currentSlide * (100 / this.slidesToShow);
            const dragPercentage = (this.dragOffset / this.$el.offsetWidth) * 100;
            return `translateX(${baseOffset + dragPercentage}%)`;
        }
    }));
});
</script>
</head>
