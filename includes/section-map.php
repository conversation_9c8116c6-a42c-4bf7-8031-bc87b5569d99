<?php
/**
 * section-map.php
 *
 * Секция с картой и контактной информацией.
 * - Использует статичное изображение карты для производительности.
 * - Содержит адрес, время работы и кнопку для построения маршрута.
 */

// Формируем URL для Яндекс.Карт
$map_link = "https://yandex.ru/maps/?rtext=~" . urlencode($schema_data['geo']['latitude'] . ',' . $schema_data['geo']['longitude']);

?>
<section id="map">
    <div class="container">
        <article class="map-info">
            <hgroup>
                <h2>Заезжайте в гости</h2>
                <p>Мы всегда рады показать вам наши камины вживую.</p>
            </hgroup>
            
            <p>
                <strong>Адрес:</strong><br>
                <?= htmlspecialchars($contacts['address']['full']) ?>
            </p>
            <p>
                <strong>Время работы:</strong><br>
                <?php foreach ($contacts['work_hours'] as $line): ?>
                    <?= htmlspecialchars($line) ?><br>
                <?php endforeach; ?>
            </p>
            <p>
                <strong>Телефон:</strong><br>
                <a href="tel:<?= htmlspecialchars($contacts['phone']['link']) ?>">
                    <?= htmlspecialchars($contacts['phone']['display']) ?>
                </a>
            </p>

            <footer>
                <a href="<?= htmlspecialchars($map_link) ?>" target="_blank" rel="noopener noreferrer" role="button" class="contrast">
                    Проложить маршрут
                </a>
            </footer>
        </article>
    </div>
</section>
