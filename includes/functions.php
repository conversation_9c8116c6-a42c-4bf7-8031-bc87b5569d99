<?php
/**
 * functions.php
 * 
 * Вспомогательные функции для проекта.
 * Содержит функции для обработки данных конфигурации.
 */

/**
 * Преобразует массив строк времени работы из config.php 
 * в структуру openingHoursSpecification для Schema.org JSON-LD
 * 
 * @param array $workHours Массив строк времени работы из $contacts['work_hours']
 * @return array Массив объектов OpeningHoursSpecification для Schema.org
 */
function parseWorkHoursForSchema($workHours) {
    $result = [];
    
    foreach ($workHours as $schedule) {
        $parsed = parseWorkHourString($schedule);
        if ($parsed) {
            $result[] = $parsed;
        }
    }
    
    return $result;
}

/**
 * Парсит одну строку времени работы и возвращает объект OpeningHoursSpecification
 * 
 * @param string $schedule Строка вида "Пн-Пт: 10 – 18 (по согласованию до 22.00)"
 * @return array|null Объект OpeningHoursSpecification или null если не удалось распарсить
 */
function parseWorkHourString($schedule) {
    // Убираем лишние пробелы и приводим к нижнему регистру для анализа
    $schedule = trim($schedule);
    
    // Проверяем на выходной день
    if (stripos($schedule, 'выходной') !== false) {
        // Определяем день недели для выходного
        if (stripos($schedule, 'воскресенье') !== false) {
            return [
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => ['Sunday'],
                'opens' => '00:00',
                'closes' => '00:00'
            ];
        }
        return null; // Если не можем определить день, пропускаем
    }
    
    // Регулярное выражение для парсинга времени работы
    // Ищем паттерн: "Дни: время1 – время2"
    // Используем более простой подход - заменим все виды тире на обычный дефис
    $schedule = str_replace(['–', '—', '−'], '-', $schedule);
    $pattern = '/^([^:]+):\s*(\d{1,2})\s*-\s*(\d{1,2})/';
    
    if (preg_match($pattern, $schedule, $matches)) {
        $daysStr = trim($matches[1]);
        $openTime = str_pad($matches[2], 2, '0', STR_PAD_LEFT) . ':00';
        $closeTime = str_pad($matches[3], 2, '0', STR_PAD_LEFT) . ':00';
        
        // Преобразуем дни недели
        $days = parseDaysOfWeek($daysStr);
        
        if (!empty($days)) {
            return [
                '@type' => 'OpeningHoursSpecification',
                'dayOfWeek' => $days,
                'opens' => $openTime,
                'closes' => $closeTime
            ];
        }
    }
    
    return null;
}

/**
 * Преобразует строку с днями недели в массив английских названий для Schema.org
 * 
 * @param string $daysStr Строка вида "Пн-Пт" или "Суббота"
 * @return array Массив английских названий дней недели
 */
function parseDaysOfWeek($daysStr) {
    $daysStr = trim($daysStr);

    // Словарь соответствий (включаем все варианты написания)
    $patterns = [
        // Диапазоны
        '/пн.*пт/ui' => ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
        '/сб.*вс/ui' => ['Saturday', 'Sunday'],

        // Отдельные дни (полные названия)
        '/понедельник/ui' => ['Monday'],
        '/вторник/ui' => ['Tuesday'],
        '/среда/ui' => ['Wednesday'],
        '/четверг/ui' => ['Thursday'],
        '/пятница/ui' => ['Friday'],
        '/суббота/ui' => ['Saturday'],
        '/воскресенье/ui' => ['Sunday'],

        // Сокращения
        '/\bпн\b/ui' => ['Monday'],
        '/\bвт\b/ui' => ['Tuesday'],
        '/\bср\b/ui' => ['Wednesday'],
        '/\bчт\b/ui' => ['Thursday'],
        '/\bпт\b/ui' => ['Friday'],
        '/\bсб\b/ui' => ['Saturday'],
        '/\bвс\b/ui' => ['Sunday']
    ];

    $result = [];

    // Проверяем каждый паттерн
    foreach ($patterns as $pattern => $days) {
        if (preg_match($pattern, $daysStr)) {
            $result = array_merge($result, $days);
            // Если нашли диапазон, возвращаем его сразу
            if (count($days) > 1) {
                return $days;
            }
        }
    }

    return array_unique($result);
}

/**
 * Отладочная функция для проверки результата парсинга
 * Можно использовать для тестирования в процессе разработки
 * 
 * @param array $workHours Массив строк времени работы
 * @return void
 */
function debugWorkHoursParsing($workHours) {
    echo "<pre style='background: #f5f5f5; padding: 10px; margin: 10px 0;'>";
    echo "Отладка парсинга времени работы:\n\n";
    
    foreach ($workHours as $index => $schedule) {
        echo "Исходная строка {$index}: {$schedule}\n";
        $parsed = parseWorkHourString($schedule);
        if ($parsed) {
            echo "Результат парсинга: " . json_encode($parsed, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
        } else {
            echo "Не удалось распарсить\n";
        }
        echo str_repeat('-', 50) . "\n";
    }
    
    echo "\nИтоговый результат для Schema.org:\n";
    $final = parseWorkHoursForSchema($workHours);
    echo json_encode($final, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    echo "</pre>";
}
