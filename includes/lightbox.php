<?php
/**
 * lightbox.php (v2 - Patched)
 *
 * Глобальный компонент для просмотра увеличенных изображений.
 * Исправлен баг с открытием при загрузке страницы.
 */
?>
<div
    id="lightbox"
    x-data="{
        isOpen: false,
        imageSrc: '',
        altText: ''
    }"
    x-cloak
    x-show="isOpen"
    @open-lightbox.window="
        isOpen = true;
        imageSrc = $event.detail.src;
        altText = $event.detail.alt;
        $nextTick(() => $refs.lightboxImage.focus());
    "
    @keydown.escape.window="isOpen = false"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="lightbox-overlay"
    tabindex="-1"
    role="dialog"
    aria-modal="true"
    :aria-hidden="!isOpen"
>
    <div class="lightbox-backdrop" @click="isOpen = false"></div>

    <div class="lightbox-content">
        <button class="close-button" @click="isOpen = false" aria-label="Закрыть изображение">×</button>
        
        <div class="image-container">
            <img 
                x-ref="lightboxImage"
                :src="imageSrc" 
                :alt="altText" 
                @click.stop 
            >
        </div>
        
        <div class="alt-text-container" x-show="altText">
            <p x-text="altText"></p>
        </div>
    </div>
</div>