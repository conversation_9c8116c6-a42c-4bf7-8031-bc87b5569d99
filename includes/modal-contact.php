<?php
/**
 * modal-contact.php
 *
 * Универсальное модальное окно для связи.
 * Управляется через Alpine.js.
 */
?>
<dialog 
    id="contact-modal"
    x-data="{ open: false, title: 'Свяжитесь с нами' }"
    :open="open"
    @open-modal.window="open = true; title = $event.detail.title || 'Свяжитесь с нами';"
    @keydown.escape.window="open = false"
>
    <article>
        <header>
            <a href="#close" aria-label="Закрыть" class="close" @click.prevent="open = false"></a>
            <h2 x-text="title"></h2>
        </header>
        
        <p>Выберите удобный способ для связи:</p>
        
        <div class="contact-options">
            <?php foreach ($contacts['messengers'] as $messenger): ?>
                <a href="<?= htmlspecialchars($messenger['link']) ?>" target="_blank" role="button" class="secondary">
                    <?= htmlspecialchars($messenger['name']) ?>
                </a>
            <?php endforeach; ?>

            <a href="tel:<?= htmlspecialchars($contacts['phone']['link']) ?>" role="button" class="contrast">
                Позвонить: <?= htmlspecialchars($contacts['phone']['display']) ?>
            </a>
        </div>

    </article>
</dialog>