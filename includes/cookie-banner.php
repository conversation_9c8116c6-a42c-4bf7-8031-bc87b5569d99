<?php
/**
 * cookie-banner.php
 *
 * Баннер с уведомлением об использовании cookie.
 * - Реализован выбор "Принять" или "Отказаться".
 */
?>
<div 
    id="cookie-banner"
    x-data="cookieBanner()"
    x-show="show"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="translate-y-full"
    x-transition:enter-end="translate-y-0"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="translate-y-0"
    x-transition:leave-end="translate-y-full"
>
    <div class="container">
        <p>
            Мы используем файлы cookie для улучшения работы сайта. Нажимая «Принять», вы соглашаетесь с нашей 
            <a href="/privacy-policy.html" target="_blank">Политикой конфиденциальности</a>.
        </p>
        <div class="cookie-buttons">
            <button class="secondary" @click="reject()">Отказаться</button>
            <button @click="accept()">Принять</button>
        </div>
    </div>
</div>

<script>
function cookieBanner() {
    return {
        show: true,
        accept() {
            const expiryDate = new Date();
            expiryDate.setFullYear(expiryDate.getFullYear() + 1);
            document.cookie = `cookie_consent=true; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
            this.show = false;
        },
        reject() {
            // Просто скрываем баннер на текущую сессию, не устанавливая долгосрочный cookie.
            // Пользователь не дал согласия.
            this.show = false;
        }
    }
}
</script>
